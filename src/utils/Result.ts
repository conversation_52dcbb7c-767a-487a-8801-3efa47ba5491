/**
 * A Result type representing either a success value or an error.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type Result<T, E = any> = Success<T> | Failure<E>;

/**
 * Success case of Result with chainable methods
 */
export interface Success<T> {
  readonly success: true;
  readonly data: T;

  // Core Railway Oriented Programming method
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  map<U>(fn: (data: T) => U | Result<U, any>): Result<U, any>;

  // Async Railway Oriented Programming method
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  mapAsync<U>(fn: (data: T) => Promise<U> | Promise<Result<U, any>>): Promise<Result<U, any>>;
}

/**
 * Failure case of Result with chainable methods
 */
export interface Failure<E> {
  readonly success: false;
  readonly error: E;

  // Core Railway Oriented Programming method
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  map<U>(fn: (data: never) => U | Result<U, any>): Result<U, any>;

  // Async Railway Oriented Programming method
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  mapAsync<U>(fn: (data: never) => Promise<U> | Promise<Result<U, any>>): Promise<Result<U, any>>;
}

/**
 * Creates a success result with chainable methods
 */
export function ok<T>(value: T): Success<T> {
  return new SuccessImpl(value);
}

/**
 * Creates a failure result with chainable methods
 */
export function err<E>(error: E): Failure<E> {
  return new FailureImpl(error);
}

/**
 * Implementation of Success with chainable methods
 */
class SuccessImpl<T> implements Success<T> {
  readonly success = true as const;

  constructor(readonly data: T) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  map<U>(fn: (data: T) => U | Result<U, any>): Result<U, any> {
    try {
      const result = fn(this.data);
      // If the function returns a Result, return it directly
      if (result && typeof result === 'object' && 'success' in result) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return result as Result<U, any>;
      }
      // Otherwise, wrap the value in a success Result
      return ok(result as U);
    } catch (error) {
      return err(error);
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async mapAsync<U>(fn: (data: T) => Promise<U> | Promise<Result<U, any>>): Promise<Result<U, any>> {
    try {
      const result = await fn(this.data);
      // If the function returns a Result, return it directly
      if (result && typeof result === 'object' && 'success' in result) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return result as Result<U, any>;
      }
      // Otherwise, wrap the value in a success Result
      return ok(result as U);
    } catch (error) {
      return err(error);
    }
  }
}

/**
 * Implementation of Failure with chainable methods
 */
class FailureImpl<E> implements Failure<E> {
  readonly success = false as const;

  constructor(readonly error: E) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
  map<U>(_fn: (data: never) => U | Result<U, any>): Result<U, any> {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this as any;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
  async mapAsync<U>(_fn: (data: never) => Promise<U> | Promise<Result<U, any>>): Promise<Result<U, any>> {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this as any;
  }
}

export function isOk<T, E>(result: Result<T, E>): result is Success<T> {
  return result.success;
}

export function isErr<T, E>(result: Result<T, E>): result is Failure<E> {
  return !result.success;
}

/**
 * Converts a Promise<T> to Promise<Result<T, E>>
 * Catches any errors and wraps them in a Failure result
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function fromPromise<T, E = any>(promise: Promise<T>): Promise<Result<T, E>> {
  try {
    const value = await promise;
    return ok(value);
  } catch (error) {
    return err(error as E);
  }
}

/**
 * Converts a Result<T, E> to Promise<T>
 * Throws the error if the result is a failure
 */
// eslint-disable-next-line local-rules/require-result-return-type
export function toPromise<T, E>(result: Result<T, E>): Promise<T> {
  if (isOk(result)) {
    return Promise.resolve(result.data);
  } else {
    return Promise.reject(result.error);
  }
}

/**
 * Maps over a Promise<Result<T, E>> with a synchronous function
 * Maintains Railway Oriented Programming pattern for async operations
 */
export async function mapPromiseResult<T, U, E>(
  promiseResult: Promise<Result<T, E>>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  fn: (data: T) => U | Result<U, any>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<Result<U, any>> {
  const result = await promiseResult;
  return result.map(fn);
}

/**
 * Maps over a Promise<Result<T, E>> with an asynchronous function
 * Maintains Railway Oriented Programming pattern for async operations
 */
export async function mapAsyncPromiseResult<T, U, E>(
  promiseResult: Promise<Result<T, E>>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  fn: (data: T) => Promise<U> | Promise<Result<U, any>>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<Result<U, any>> {
  const result = await promiseResult;
  return result.mapAsync(fn);
}
