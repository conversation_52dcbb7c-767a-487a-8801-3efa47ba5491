import { ok, err, Result, isOk, isErr } from './Result.js'

// Demo: Using .then() chains with the new Promise-based map method

async function demoThenChaining() {
  console.log('=== Demo: .then() chaining with Result.map() ===\n')

  // Example 1: Simple transformation chain using .then()
  console.log('1. Simple transformation chain:')
  const result1 = await ok(5)
    .map(x => x * 2)
    .then(r => r.map(x => x.toString()))
    .then(r => r.map(x => `Result: ${x}`))

  if (isOk(result1)) {
    console.log(`   Success: ${result1.data}`) // "Result: 10"
  }

  // Example 2: Chain with error handling
  console.log('\n2. Chain with error handling:')
  const result2 = await ok(10)
    .map(x => x * 2)
    .then(r => r.map(x => {
      if (x > 15) throw new Error('Too large!')
      return x
    }))
    .then(r => r.map(x => x.toString()))

  if (isErr(result2)) {
    console.log(`   Error: ${result2.error.message}`) // "Too large!"
  }

  // Example 3: Mixed sync/async operations
  console.log('\n3. Mixed sync/async operations:')
  const asyncDouble = async (x: number): Promise<number> => {
    await new Promise(resolve => setTimeout(resolve, 10)) // simulate async
    return x * 2
  }

  const result3 = await ok(3)
    .map(x => x + 1) // sync
    .then(r => r.map(asyncDouble)) // async
    .then(r => r.map(x => `Final: ${x}`)) // sync

  if (isOk(result3)) {
    console.log(`   Success: ${result3.data}`) // "Final: 8"
  }

  // Example 4: Result-returning functions in chain
  console.log('\n4. Result-returning functions:')
  const validate = (x: number): Result<number, string> =>
    x > 0 ? ok(x) : err('Must be positive')

  const result4 = await ok(-5)
    .map(validate)
    .then(r => r.map(x => x * 2))

  if (isErr(result4)) {
    console.log(`   Error: ${result4.error}`) // "Must be positive"
  }

  // Example 5: Complex pipeline with .then() chaining
  console.log('\n5. Complex validation pipeline:')
  const processInput = async (input: string) => {
    return await ok(input)
      .map(s => s.trim())
      .then(r => r.map(s => s.length > 0 ? ok(s) : err('Empty input')))
      .then(r => r.map(s => parseInt(s)))
      .then(r => r.map(n => isNaN(n) ? err('Not a number') : ok(n)))
      .then(r => r.map(n => n > 0 ? ok(n) : err('Must be positive')))
      .then(r => r.map(n => n * 2))
  }

  const validInput = await processInput('  42  ')
  if (isOk(validInput)) {
    console.log(`   Valid input result: ${validInput.data}`) // 84
  }

  const invalidInput = await processInput('  -5  ')
  if (isErr(invalidInput)) {
    console.log(`   Invalid input error: ${invalidInput.error}`) // "Must be positive"
  }

  console.log('\n=== Demo completed ===')
}

// Run the demo
demoThenChaining().catch(console.error)
