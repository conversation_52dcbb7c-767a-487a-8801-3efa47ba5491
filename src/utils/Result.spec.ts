import { describe, it, expect } from 'vitest'
import { ok, err, isOk, isErr, type Result } from './Result'

describe('Result', () => {
  describe('ok', () => {
    it('should create a success result', () => {
      const result = ok(42)
      expect(result.success).toBe(true)
      expect(result.data).toBe(42)
    })

    it('should be identified as success by isOk', () => {
      const result = ok('test')
      expect(isOk(result)).toBe(true)
      expect(isErr(result)).toBe(false)
    })
  })

  describe('err', () => {
    it('should create a failure result', () => {
      const result = err('error message')
      expect(result.success).toBe(false)
      expect(result.error).toBe('error message')
    })

    it('should be identified as error by isErr', () => {
      const result = err('error')
      expect(isErr(result)).toBe(true)
      expect(isOk(result)).toBe(false)
    })
  })

  describe('map method chaining', () => {
    it('should chain successful transformations', async () => {
      const result = await ok(5)
        .map(x => x * 2)
        .then(r => r.map(x => x.toString()))
        .then(r => r.map(x => x + '!'))

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.data).toBe('10!')
      }
    })

    it('should stop at first error in chain', async () => {
      const result = await ok(5)
        .map(x => x * 2)
        .then(r => r.map(x => {
          if (x > 5) throw new Error('Too large')
          return x
        }))
        .then(r => r.map(x => x.toString()))

      expect(isErr(result)).toBe(true)
      if (isErr(result)) {
        expect(result.error).toBeInstanceOf(Error)
        expect((result.error as Error).message).toBe('Too large')
      }
    })

    it('should handle Result-returning functions in map', async () => {
      const result = await ok(5)
        .map(x => x > 3 ? ok(x * 2) : err('Too small'))
        .then(r => r.map(x => x.toString()))

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.data).toBe('10')
      }
    })

    it('should propagate errors from Result-returning functions', async () => {
      const result = await ok(2)
        .map(x => x > 3 ? ok(x * 2) : err('Too small'))
        .then(r => r.map(x => x.toString()))

      expect(isErr(result)).toBe(true)
      if (isErr(result)) {
        expect(result.error).toBe('Too small')
      }
    })

    it('should not execute map on error results', async () => {
      let executed = false
      const result = await err('initial error')
        .map(x => {
          executed = true
          return x
        })

      expect(executed).toBe(false)
      expect(isErr(result)).toBe(true)
      if (isErr(result)) {
        expect(result.error).toBe('initial error')
      }
    })
  })

  describe('Railway Oriented Programming patterns', () => {
    it('should validate and transform data in a pipeline', async () => {
      const validatePositive = (n: number): Result<number, string> =>
        n > 0 ? ok(n) : err('Number must be positive')

      const validateLessThan100 = (n: number): Result<number, string> =>
        n < 100 ? ok(n) : err('Number must be less than 100')

      const result = await ok(50)
        .map(validatePositive)
        .then(r => r.map(validateLessThan100))
        .then(r => r.map(n => n * 2))

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.data).toBe(100)
      }
    })

    it('should fail early in validation pipeline', async () => {
      const validatePositive = (n: number): Result<number, string> =>
        n > 0 ? ok(n) : err('Number must be positive')

      const validateLessThan100 = (n: number): Result<number, string> =>
        n < 100 ? ok(n) : err('Number must be less than 100')

      const result = await ok(-5)
        .map(validatePositive)
        .then(r => r.map(validateLessThan100))
        .then(r => r.map(n => n * 2))

      expect(isErr(result)).toBe(true)
      if (isErr(result)) {
        expect(result.error).toBe('Number must be positive')
      }
    })

    it('should handle complex data processing', async () => {
      interface User {
        name: string
        age: number
      }

      const validateName = (user: User): Result<User, string> =>
        user.name.trim().length > 0 ? ok(user) : err('Name cannot be empty')

      const validateAge = (user: User): Result<User, string> =>
        user.age >= 18 ? ok(user) : err('User must be 18 or older')

      const normalizeUser = (user: User): User => ({
        ...user,
        name: user.name.trim().toUpperCase()
      })

      const result = await ok({ name: '  john  ', age: 25 })
        .map(normalizeUser)
        .then(r => r.map(validateName))
        .then(r => r.map(validateAge))

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.data.name).toBe('JOHN')
        expect(result.data.age).toBe(25)
      }
    })

    it('should handle JSON parsing with error handling', async () => {
      const parseJson = (jsonStr: string): unknown => {
        return JSON.parse(jsonStr)
      }

      const validResult = await ok('{"name": "test"}')
        .map(parseJson)
        .then(r => r.map(obj => (obj as { name: string }).name))

      expect(isOk(validResult)).toBe(true)
      if (isOk(validResult)) {
        expect(validResult.data).toBe('test')
      }

      const invalidResult = await ok('invalid json')
        .map(parseJson)
        .then(r => r.map(obj => (obj as { name: string }).name))

      expect(isErr(invalidResult)).toBe(true)
    })
  })

  describe('type safety', () => {
    it('should maintain type safety through transformations', async () => {
      const result: Promise<Result<string, Error>> = ok(42)
        .map(x => x.toString())
        .then(r => r.map(s => s.length > 0 ? ok(s) : err(new Error('Empty string'))))

      const finalResult = await result
      expect(isOk(finalResult)).toBe(true)
      if (isOk(finalResult)) {
        expect(typeof finalResult.data).toBe('string')
        expect(finalResult.data).toBe('42')
      }
    })
  })

  describe('real-world usage examples', () => {
    it('should handle user input validation pipeline', async () => {
      const processUserInput = async (input: string) =>
        await ok(input)
          .map(s => s.trim())
          .then(r => r.map(s => s.length > 0 ? ok(s) : err('Input cannot be empty')))
          .then(r => r.map(s => s.length <= 50 ? ok(s) : err('Input too long')))
          .then(r => r.map(s => parseInt(s)))
          .then(r => r.map(n => isNaN(n) ? err('Must be a number') : ok(n)))
          .then(r => r.map(n => n > 0 ? ok(n) : err('Must be positive')))

      // Valid input
      const validResult = await processUserInput('  42  ')
      expect(isOk(validResult)).toBe(true)
      if (isOk(validResult)) {
        expect(validResult.data).toBe(42)
      }

      // Invalid input - empty
      const emptyResult = await processUserInput('   ')
      expect(isErr(emptyResult)).toBe(true)
      if (isErr(emptyResult)) {
        expect(emptyResult.error).toBe('Input cannot be empty')
      }

      // Invalid input - not a number
      const nanResult = await processUserInput('abc')
      expect(isErr(nanResult)).toBe(true)
      if (isErr(nanResult)) {
        expect(nanResult.error).toBe('Must be a number')
      }
    })


    it('should handle async-like operations with map chaining', async () => {
      // Simulating async operations that return Results
      const fetchUser = (id: number): Result<{ id: number, name: string }, string> =>
        id > 0 ? ok({ id, name: `User${id}` }) : err('Invalid user ID')

      const validateUser = (user: { id: number, name: string }): Result<{ id: number, name: string }, string> =>
        user.name.length > 0 ? ok(user) : err('Invalid user name')

      const enrichUser = (user: { id: number, name: string }) => ({
        ...user,
        displayName: `${user.name} (#${user.id})`
      })

      const result = await ok(123)
        .map(fetchUser)
        .then(r => r.map(validateUser))
        .then(r => r.map(enrichUser))

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.data.displayName).toBe('User123 (#123)')
      }
    })
  })
})
